#!/usr/bin/env python3
"""
Football News Bot - Easy Run Script
بوت أخبار كرة القدم - سكريبت تشغيل مبسط

This script provides an easy way to run the football news bot with different options.
"""

import sys
import os
import subprocess
import time
from utils.logger import logger

def print_banner():
    """Print a nice banner"""
    banner = """
    ⚽ ========================================== ⚽
    🤖        بوت أخبار كرة القدم        🤖
    ⚽        Football News Bot        ⚽
    ========================================== 
    """
    print(banner)

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import requests
        import beautifulsoup4
        import selenium
        import google.auth
        import google.generativeai
        import telegram
        import PIL
        import dotenv
        logger.info("✅ All requirements are installed")
        return True
    except ImportError as e:
        logger.error(f"❌ Missing requirement: {e}")
        print(f"❌ Missing requirement: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists"""
    if os.path.exists('.env'):
        logger.info("✅ .env file found")
        return True
    else:
        logger.warning("⚠️ .env file not found")
        print("⚠️ .env file not found. Please create it from env.example")
        return False

def run_test_mode():
    """Run the bot in test mode (single cycle)"""
    print("🧪 Running in TEST mode (single cycle)...")
    logger.info("Starting bot in test mode")
    
    try:
        result = subprocess.run([sys.executable, 'main.py', '--test'], 
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("✅ Test completed successfully!")
            logger.info("Test mode completed successfully")
        else:
            print("❌ Test failed!")
            logger.error("Test mode failed")
    except Exception as e:
        print(f"❌ Error running test: {e}")
        logger.error(f"Error running test: {e}")

def run_production_mode():
    """Run the bot in production mode (continuous)"""
    print("🚀 Running in PRODUCTION mode (continuous)...")
    print("Press Ctrl+C to stop the bot")
    logger.info("Starting bot in production mode")
    
    try:
        subprocess.run([sys.executable, 'main.py'], capture_output=False, text=True)
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
        logger.info("Bot stopped by user")
    except Exception as e:
        print(f"❌ Error running bot: {e}")
        logger.error(f"Error running bot: {e}")

def run_component_tests():
    """Run individual component tests"""
    print("🔧 Running component tests...")
    
    tests = [
        ("Database", "test_database.py"),
        ("Blogger Publisher", "test_blogger.py"),
        ("Telegram Publisher", "test_telegram.py"),
        ("All Components", "tests/test_all_components.py")
    ]
    
    for test_name, test_file in tests:
        if os.path.exists(test_file):
            print(f"\n🧪 Testing {test_name}...")
            try:
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    print(f"✅ {test_name} test passed")
                else:
                    print(f"❌ {test_name} test failed")
                    if result.stderr:
                        print(f"Error: {result.stderr[:200]}...")
            except subprocess.TimeoutExpired:
                print(f"⏰ {test_name} test timed out")
            except Exception as e:
                print(f"❌ Error testing {test_name}: {e}")
        else:
            print(f"⚠️ {test_name} test file not found: {test_file}")

def show_status():
    """Show system status"""
    print("📊 System Status:")
    print("-" * 40)
    
    # Check files
    files_to_check = [
        (".env", "Environment file"),
        ("requirements.txt", "Requirements file"),
        ("database/news.db", "Database file"),
        ("main.py", "Main script"),
    ]
    
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {description}: Found")
        else:
            print(f"❌ {description}: Missing")
    
    # Check directories
    dirs_to_check = ["scraper", "generator", "publisher", "utils", "database"]
    for dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/ directory: Found")
        else:
            print(f"❌ {dir_path}/ directory: Missing")

def main():
    """Main function"""
    print_banner()
    
    # Basic checks
    if not check_requirements():
        return
    
    check_env_file()
    
    while True:
        print("\n🎯 Choose an option:")
        print("1. 🧪 Run Test Mode (single cycle)")
        print("2. 🚀 Run Production Mode (continuous)")
        print("3. 🔧 Run Component Tests")
        print("4. 📊 Show System Status")
        print("5. 📖 View System Report")
        print("6. 🚪 Exit")
        
        try:
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == '1':
                run_test_mode()
            elif choice == '2':
                run_production_mode()
            elif choice == '3':
                run_component_tests()
            elif choice == '4':
                show_status()
            elif choice == '5':
                if os.path.exists('SYSTEM_STATUS_REPORT.md'):
                    print("\n📖 Opening system report...")
                    with open('SYSTEM_STATUS_REPORT.md', 'r', encoding='utf-8') as f:
                        print(f.read())
                else:
                    print("❌ System report not found")
            elif choice == '6':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
