from scraper.mock_requests import get, exceptions
from bs4 import BeautifulSoup
from config import KOOORA_URL
from utils.logger import logger
import time
import random

def scrape_kooora():
    """Scrapes the latest football news from Kooora using requests (faster alternative).

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting simple scrape for Kooora.")
    articles = []
    
    try:
        # Add headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        response = get(KOOORA_URL, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Try to find the latest news section first
        latest_news_selectors = [
            '.latest-news a',  # Latest news section
            '.breaking-news a',  # Breaking news
            '.recent-news a',  # Recent news
            '.news-list a',  # News list
            '.main-news a',  # Main news
            '.top-news a',  # Top news
            'section[class*="news"] a',  # News sections
            'div[class*="news"] a',  # News divs
            '.content a[href*="/news/"]',  # News links in content
            'main a[href*="/news/"]'  # News links in main
        ]

        # Try multiple selectors to find news articles
        selectors_to_try = [
            'a[href*="/news/"]',  # News links
            'a[href*="/sport/"]',  # Sport links
            'a[href*="/football/"]',  # Football links
            '.news-item a',
            '.news-title a',
            'article a',
            '.article-title a',
            'h3 a',
            'h2 a',
            '.title a'
        ]

        # Combine latest news selectors with general selectors
        all_selectors = latest_news_selectors + selectors_to_try
        
        found_articles = []

        for selector in all_selectors:
            try:
                logger.info(f"Trying selector: {selector}")
                links = soup.select(selector)

                for link in links:
                    try:
                        href = link.get('href')
                        text = link.get_text(strip=True)
                        title_attr = link.get('title', '')

                        # Skip if no href or text
                        if not href or not text:
                            continue

                        # Skip navigation and non-news items
                        skip_keywords = [
                            'تسجيل', 'دخول', 'حساب', 'إعدادات', 'بحث', 'قائمة',
                            'الرئيسية', 'اتصل', 'عنا', 'سياسة', 'شروط', 'خصوصية',
                            'login', 'register', 'search', 'menu', 'home', 'contact',
                            'about', 'privacy', 'terms', 'policy'
                        ]

                        if any(skip in text.lower() for skip in skip_keywords):
                            continue

                        # Make URL absolute
                        if href.startswith('/'):
                            href = f"https://www.kooora.com{href}"
                        elif not href.startswith('http'):
                            continue

                        # Check if this looks like a real news article
                        # Must have news indicators in URL or be a substantial article
                        url_news_indicators = ['/news/', '/sport/', '/football/', '/match/']
                        has_news_url = any(indicator in href.lower() for indicator in url_news_indicators)

                        # Check for football content keywords
                        football_keywords = [
                            'كرة', 'مباراة', 'فوز', 'هدف', 'لاعب', 'فريق', 'دوري', 'كأس',
                            'بطولة', 'مدرب', 'نادي', 'منتخب', 'ملعب', 'حكم', 'إصابة',
                            'انتقال', 'صفقة', 'عقد', 'تجديد', 'استقالة', 'إقالة'
                        ]

                        has_football_content = any(keyword in text.lower() or keyword in title_attr.lower()
                                                 for keyword in football_keywords)

                        # Filter for quality articles
                        if (len(text) > 15 and  # Substantial title
                            (has_news_url or has_football_content) and  # Relevant content
                            'kooora.com' in href and  # From Kooora
                            not any(skip in href.lower() for skip in ['login', 'register', 'search'])):  # Not utility pages

                            # Clean up title
                            clean_title = text.replace('\n', ' ').replace('\t', ' ')
                            clean_title = ' '.join(clean_title.split())  # Remove extra whitespace

                            # Remove common prefixes/suffixes
                            clean_title = clean_title.replace('كووورة |', '').strip()
                            clean_title = clean_title.replace('| كووورة', '').strip()

                            if len(clean_title) > 15:  # Ensure substantial content after cleaning
                                found_articles.append({
                                    'title': clean_title,
                                    'url': href,
                                    'source': 'Kooora'
                                })

                                if len(found_articles) >= 15:  # Get more to have better selection
                                    break

                    except Exception as e:
                        logger.warning(f"Error processing link: {e}")
                        continue

                if found_articles:
                    logger.info(f"Found {len(found_articles)} articles with selector: {selector}")
                    break

            except Exception as e:
                logger.warning(f"Error with selector {selector}: {e}")
                continue
        
        # Remove duplicates and prioritize news articles
        seen_urls = set()
        seen_titles = set()

        # Sort articles to prioritize news URLs
        found_articles.sort(key=lambda x: (
            '/news/' not in x['url'].lower(),  # News articles first
            '/sport/' not in x['url'].lower(),  # Then sport articles
            len(x['title'])  # Then by title length (longer = more detailed)
        ))

        for article in found_articles:
            # Skip duplicates by URL
            if article['url'] in seen_urls:
                continue

            # Skip very similar titles
            title_words = set(article['title'].lower().split())
            is_similar = False
            for seen_title in seen_titles:
                seen_words = set(seen_title.lower().split())
                # If more than 70% of words are the same, consider it duplicate
                if len(title_words & seen_words) / max(len(title_words), len(seen_words)) > 0.7:
                    is_similar = True
                    break

            if not is_similar:
                articles.append(article)
                seen_urls.add(article['url'])
                seen_titles.add(article['title'])
                logger.info(f"Found article: {article['title'][:50]}...")

                # Limit to 10 quality articles
                if len(articles) >= 10:
                    break
                
    except exceptions.RequestException as e:
        logger.error(f"Failed to scrape Kooora: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during Kooora scraping: {e}")

    logger.info(f"Found {len(articles)} articles from Kooora.")
    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_kooora()
    if scraped_articles:
        for article in scraped_articles:
            print(article)
