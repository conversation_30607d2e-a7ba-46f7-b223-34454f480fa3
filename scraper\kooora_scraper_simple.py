from scraper.mock_requests import get, exceptions
from bs4 import BeautifulSoup
from config import KOOORA_URL
from utils.logger import logger
import time
import random

def scrape_kooora():
    """Scrapes the latest football news from Kooora using requests (faster alternative).

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting simple scrape for Kooora.")
    articles = []
    
    try:
        # Add headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        response = get(KOOORA_URL, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Try multiple selectors to find news articles
        selectors_to_try = [
            'a[href*="/news/"]',  # News links
            'a[href*="/sport/"]',  # Sport links
            'a[href*="/football/"]',  # Football links
            '.news-item a',
            '.news-title a',
            'article a',
            '.article-title a',
            'h3 a',
            'h2 a',
            '.title a',
            'a'  # All links as fallback
        ]
        
        found_articles = []
        
        for selector in selectors_to_try:
            try:
                logger.info(f"Trying selector: {selector}")
                links = soup.select(selector)
                
                for link in links:
                    try:
                        href = link.get('href')
                        text = link.get_text(strip=True)
                        title_attr = link.get('title', '')
                        
                        # Skip if no href or text
                        if not href or not text:
                            continue
                            
                        # Make URL absolute
                        if href.startswith('/'):
                            href = f"https://www.kooora.com{href}"
                        elif not href.startswith('http'):
                            continue
                            
                        # Check if this looks like a football news article
                        football_keywords = ['كرة', 'مباراة', 'فوز', 'هدف', 'لاعب', 'فريق', 'دوري', 'كأس', 'بطولة']
                        is_football = any(keyword in text.lower() or keyword in title_attr.lower() for keyword in football_keywords)
                        
                        # Also check URL for football indicators
                        url_indicators = ['sport', 'football', 'news', 'kooora.com']
                        has_url_indicator = any(indicator in href.lower() for indicator in url_indicators)
                        
                        if (len(text) > 10 and 
                            (is_football or has_url_indicator) and
                            'kooora.com' in href):
                            
                            # Clean up title
                            clean_title = text.replace('\n', ' ').replace('\t', ' ')
                            clean_title = ' '.join(clean_title.split())  # Remove extra whitespace
                            
                            if len(clean_title) > 10:
                                found_articles.append({
                                    'title': clean_title,
                                    'url': href,
                                    'source': 'Kooora'
                                })
                                
                                if len(found_articles) >= 10:
                                    break
                                    
                    except Exception as e:
                        logger.warning(f"Error processing link: {e}")
                        continue
                
                if found_articles:
                    logger.info(f"Found {len(found_articles)} articles with selector: {selector}")
                    break
                    
            except Exception as e:
                logger.warning(f"Error with selector {selector}: {e}")
                continue
        
        # Remove duplicates based on URL
        seen_urls = set()
        for article in found_articles:
            if article['url'] not in seen_urls:
                articles.append(article)
                seen_urls.add(article['url'])
                logger.info(f"Found article: {article['title'][:50]}...")
                
    except exceptions.RequestException as e:
        logger.error(f"Failed to scrape Kooora: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during Kooora scraping: {e}")

    logger.info(f"Found {len(articles)} articles from Kooora.")
    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_kooora()
    if scraped_articles:
        for article in scraped_articles:
            print(article)
