
from scraper.mock_selenium import webdriver, Options, By, Service, ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from config import <PERSON><PERSON><PERSON><PERSON><PERSON>_URL, PROXY_SERVER, PROXY_PORT
from utils.logger import logger
import time
import random

def scrape_kooora():
    """Scrapes the latest football news from Kooora.

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting scrape for Kooor<PERSON>.")
    articles = []

    # Setup Chrome options for headless mode and resource optimization
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")

    # Use a more recent user agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Disable automation detection
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Add proxy if configured
    if PROXY_SERVER and PROXY_PORT:
        proxy_address = f"{PROXY_SERVER}:{PROXY_PORT}"
        chrome_options.add_argument(f"--proxy-server={proxy_address}")
        logger.info(f"Using proxy: {proxy_address}")

    # SSL and security settings
    chrome_options.add_argument("--ignore-certificate-errors")
    chrome_options.add_argument("--ignore-ssl-errors")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")

    # Memory and resource management
    chrome_options.add_argument("--memory-pressure-off")
    chrome_options.add_argument("--max_old_space_size=4096")

    # Disable unnecessary features (only disable notifications and popups)
    chrome_options.add_experimental_option("prefs", {
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 2,
        "profile.managed_default_content_settings.media_stream": 2,
    })

    # Maximum number of retries
    max_retries = 3
    retry_count = 0

    driver = None
    while retry_count < max_retries:
        try:
            # Use webdriver-manager to handle the driver automatically
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set timeouts - increased for better reliability
            driver.set_page_load_timeout(120)  # Increased to 2 minutes
            driver.implicitly_wait(10)

            # Navigate to the URL
            logger.info(f"Attempting to access {KOOORA_URL} (Attempt {retry_count + 1}/{max_retries})")

            # Try to load the page with error handling
            try:
                driver.get(KOOORA_URL)
                logger.info("Page loaded successfully")
            except Exception as e:
                logger.warning(f"Page load issue: {e}")
                # Try to continue anyway

            # Add a delay to let the page settle
            time.sleep(random.uniform(3, 7))

            # Try multiple selectors to find news items
            news_elements = []
            selectors_to_try = [
                'a[href*="/news/"]',  # Links containing /news/
                '.news-item',
                '.news-title',
                'a[href*="kooora.com"]',
                '.article-title',
                'h3 a',
                'h4 a',
                '.title a'
            ]

            for selector in selectors_to_try:
                try:
                    logger.info(f"Trying selector: {selector}")
                    WebDriverWait(driver, 30).until(
                        EC.visibility_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    news_elements = driver.find_elements(By.CSS_SELECTOR, selector)[:10]
                    if news_elements:
                        logger.info(f"Found {len(news_elements)} elements with selector: {selector}")
                        break
                except TimeoutException:
                    logger.warning(f"Selector {selector} not found, trying next...")
                    continue
                except Exception as e:
                    logger.warning(f"Error with selector {selector}: {e}")
                    continue

            # If no elements found with any selector, try to get all links and filter
            if not news_elements:
                logger.warning("No news elements found with any selector. Trying to get all links...")
                all_links = driver.find_elements(By.TAG_NAME, 'a')
                news_elements = [link for link in all_links if link.get_attribute('href') and
                               ('news' in link.get_attribute('href').lower() or
                                'sport' in link.get_attribute('href').lower() or
                                'football' in link.get_attribute('href').lower())][:10]
                logger.info(f"Found {len(news_elements)} potential news links")

            for elem in news_elements:
                try:
                    # Get the URL first
                    url = elem.get_attribute('href')
                    if not url or not url.startswith('http'):
                        continue

                    # Try to get title from different sources
                    title = ""

                    # Try to find title in child elements first
                    title_selectors = ['h1', 'h2', 'h3', 'h4', 'h5', '.title', '.news-title', '.headline']
                    for selector in title_selectors:
                        try:
                            title_elem = elem.find_element(By.CSS_SELECTOR, selector)
                            if title_elem and title_elem.text.strip():
                                title = title_elem.text.strip()
                                break
                        except:
                            continue

                    # If no title found in child elements, use the element's text
                    if not title:
                        title = elem.text.strip()

                    # If still no title, try to get it from title attribute
                    if not title:
                        title = elem.get_attribute('title') or ""

                    # Clean up the title
                    title = title.replace('\n', ' ').replace('\t', ' ')
                    title = ' '.join(title.split())  # Remove extra whitespace

                    # Validate title and URL
                    if title and len(title) > 10 and url and 'kooora.com' in url:
                        articles.append({
                            'title': title,
                            'url': url,
                            'source': 'Kooora'
                        })
                        logger.info(f"Found article: {title[:50]}...")

                except Exception as e:
                    logger.warning(f"Could not process a news item from Kooora: {e}")
                    continue

            # If we got here without exceptions, break the retry loop
            break
            
        except TimeoutException as e:
            retry_count += 1
            logger.warning(f"Timeout while accessing Kooora (Attempt {retry_count}/{max_retries}): {str(e)[:200]}...")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            if retry_count < max_retries:
                wait_time = 10 * retry_count  # Exponential backoff
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

        except WebDriverException as e:
            retry_count += 1
            logger.warning(f"WebDriver error while accessing Kooora (Attempt {retry_count}/{max_retries}): {str(e)[:200]}...")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            if retry_count < max_retries:
                wait_time = 10 * retry_count
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

        except Exception as e:
            logger.error(f"Unexpected error while scraping Kooora: {e}")
            retry_count += 1
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            if retry_count < max_retries:
                logger.info("Retrying after unexpected error...")
                time.sleep(5)

        finally:
            # Clean up driver if still exists
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None

    # Log results
    if articles:
        logger.info(f"Successfully found {len(articles)} articles from Kooora after {retry_count + 1} attempts.")
    else:
        logger.warning(f"Found 0 articles from Kooora after {retry_count + 1} attempts.")

    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_kooora()
    if scraped_articles:
        for article in scraped_articles:
            print(article)
