
# Football News Bot

This project is a Python-based bot that automatically scrapes football news, generates detailed articles using the Gemini API, and publishes them to Blogger and Telegram.

## Features

- Scrapes news from multiple sources (Sky News Arabia, Kooora).
- Uses a pool of 45+ Gemini API keys for content generation.
- Generates 2500-word articles in Egyptian Arabic.
- Publishes articles to Blogger with SEO-optimized keywords.
- Sends notifications to a Telegram channel.
- Uses a local SQLite database to prevent duplicate posts.

## Project Structure

- `main.py`: The main entry point for the bot.
- `config.py`: Configuration file (reads from `.env`).
- `.env.example`: An example file for environment variables.
- `requirements.txt`: A list of all necessary Python packages.
- `database/`: Contains the SQLite database logic.
- `scraper/`: Modules for scraping news from different sources.
- `generator/`: Modules for generating content and images.
- `publisher/`: Modules for publishing content.
- `utils/`: Utility modules for logging and API key management.
- `tests/`: A suite of unit tests to verify the functionality of the application.

## Setup and Installation

1.  **Clone the repository.**
2.  **Create a virtual environment:**
    ```bash
    python -m venv venv
    source venv/bin/activate
    ```
3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
4.  **Create your `.env` file:**
    Copy `.env.example` to a new file named `.env` and fill in your API keys and other credentials.
    ```bash
    cp .env.example .env
    ```

## Running the Bot

To run the bot in its continuous loop:
```bash
python main.py
```

## Running Tests

To run all unit tests:
```bash
python -m unittest discover tests
```

## Troubleshooting

- **`ModuleNotFoundError`**: Make sure you have installed all the packages from `requirements.txt` in your active virtual environment.
- **`ValueError: No Gemini API keys found`**: Ensure your `.env` file is correctly formatted and contains at least one `GEMINI_API_KEY_...` entry.
- **Google OAuth Errors**: If you have issues with Blogger publishing, your `GOOGLE_OAUTH_TOKEN` might be expired or invalid. You may need to re-authenticate and generate a new token.
- **Scraping Issues**: Websites change their layout. If a scraper stops working, you may need to update the CSS selectors in the corresponding scraper file (`scraper/sky_news_scraper.py` or `scraper/kooora_scraper.py`).
