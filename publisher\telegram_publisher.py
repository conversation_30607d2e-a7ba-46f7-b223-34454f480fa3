
import telegram
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID
from utils.logger import logger

async def send_telegram_notification(title, post_url):
    """Sends a notification to the Telegram channel.

    Args:
        title (str): The title of the new post.
        post_url (str): The URL of the new post on Blogger.
    """
    logger.info(f"Sending Telegram notification for: {title}")
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHANNEL_ID:
        logger.warning("Telegram token or channel ID is not set. Skipping notification.")
        return

    try:
        bot = telegram.Bot(token=TELEGRAM_BOT_TOKEN)
        message = f"⚽ **خبر جديد وصل!** ⚽\n\n**العنوان:** {title}\n\n**اقرأ المقال كاملًا من هنا:**\n{post_url}"
        
        await bot.send_message(
            chat_id=TELEGRAM_CHANNEL_ID,
            text=message,
            parse_mode='Markdown'
        )
        logger.info("Telegram notification sent successfully.")

    except Exception as e:
        logger.error(f"Failed to send Telegram notification: {e}")

if __name__ == '__main__':
    # For testing purposes (requires Telegram token and channel ID in .env)
    import asyncio
    test_title = "اختبار: الأهلي بطلًا للدوري"
    test_url = "https://example.com/test-post"
    # asyncio.run(send_telegram_notification(test_title, test_url))
    print("Telegram publisher test script. Uncomment the line above to run a live test.")
