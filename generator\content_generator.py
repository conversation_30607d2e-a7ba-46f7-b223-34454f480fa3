
from utils.gemini_manager import gemini_manager
from utils.logger import logger

def generate_seo_keywords(topic):
    """Generates SEO keywords for a given topic using Gemini."""
    try:
        model = gemini_manager.get_model()
        prompt = f"أعطني 10 كلمات مفتاحية (Keywords) قوية ومناسبة لمحركات البحث (SEO) لمقال عن: '{topic}'. قدم الكلمات فقط مفصولة بفاصلة."
        response = model.generate_content(prompt)
        return response.text.strip().split(',')
    except Exception as e:
        logger.error(f"Failed to generate SEO keywords: {e}")
        return [topic] # Fallback to the topic itself

def generate_mock_article(title, source, keywords):
    """Generates a mock article for testing purposes."""
    mock_content = f"""
# {title}

## مقدمة
في تطور مثير في عالم كرة القدم، {title.lower()}. هذا الخبر الذي نقلته وكالة {source} يأتي في وقت حساس من الموسم الرياضي.

## تفاصيل الحدث
تشير المصادر إلى أن هذا الحدث له تأثير كبير على المشهد الرياضي. الجماهير تتابع بشغف كبير هذه التطورات المهمة.

### ردود الأفعال
- الجماهير متحمسة للغاية
- النقاد الرياضيون يحللون الوضع
- اللاعبون يستعدون للمرحلة القادمة

## التحليل الفني
من الناحية الفنية، هذا الحدث يمثل نقطة تحول مهمة. الخبراء يرون أن التأثير سيكون إيجابياً على المدى الطويل.

### الإحصائيات
- معدل الأداء: ممتاز
- مستوى الحماس: عالي جداً
- التوقعات المستقبلية: إيجابية

## خلاصة
في النهاية، هذا الحدث يؤكد على أهمية كرة القدم في حياتنا اليومية. نتطلع لمزيد من التطورات الإيجابية في المستقبل القريب.

**الكلمات المفتاحية:** {', '.join(keywords)}
"""
    return mock_content

def generate_article(title, source):
    """Generates a 2500-word article in Egyptian Arabic using Gemini.

    Args:
        title (str): The title of the news article.
        source (str): The source of the news.

    Returns:
        tuple: A tuple containing the generated article (str) and a list of keywords (list).
    """
    logger.info(f"Generating article for: '{title}'")

    # First generate keywords (fallback to title-based keywords if Gemini fails)
    keywords = generate_seo_keywords(title)
    logger.info(f"Generated SEO keywords: {keywords}")

    try:
        model = gemini_manager.get_model()

        # 2. Construct the main prompt
        prompt = f"""
        مهمتك هي كتابة مقال صحفي احترافي ومفصل من 2500 كلمة بالضبط عن الخبر التالي: '{title}' من مصدر '{source}'.

        **قواعد الكتابة:**
        1.  **اللهجة:** استخدم العامية المصرية بشكل كامل. يجب أن تكون اللغة طبيعية وجذابة للقارئ المصري.
        2.  **الطول:** المقال يجب أن يكون 2500 كلمة بالضبط. هذا شرط أساسي.
        3.  **الأسلوب:** أسلوب صحفي شيق، ابدأ بمقدمة قوية، ثم حلل الخبر من كل جوانبه، وقدم خلفية عن الموضوع، وتوقعات مستقبلية. استخدم فقرات قصيرة وعناوين فرعية لتنظيم المقال.
        4.  **الكلمات المفتاحية:** يجب دمج الكلمات المفتاحية التالية بشكل طبيعي في المقال: {', '.join(keywords)}
        5.  **المحتوى:** كن مبدعًا وتوسع في الخبر. يمكنك إضافة تفاصيل من وحي خيالك طالما أنها تخدم القصة وواقعية في سياق كرة القدم (مثل تصريحات متخيلة من اللاعبين أو المدربين، تحليل فني للمباريات، ردود فعل الجماهير).
        6.  **التنسيق:** استخدم Markdown لتنسيق العناوين والنقاط والقوائم.

        ابدأ الآن في كتابة المقال.
        """

        response = model.generate_content(prompt)
        article_content = response.text
        logger.info(f"Successfully generated article for '{title}'.")
        return article_content, keywords

    except Exception as e:
        logger.error(f"Failed to generate article for '{title}': {e}")
        logger.info(f"Falling back to mock article for '{title}'")
        mock_article = generate_mock_article(title, source, keywords)
        return mock_article, keywords

if __name__ == '__main__':
    # For testing purposes (requires Gemini API keys in .env)
    test_title = "الأهلي يفوز على الزمالك في قمة الدوري المصري"
    test_source = "Kooora"
    article, keywords = generate_article(test_title, test_source)
    if article:
        print(f"**Article for: {test_title}**")
        print(f"**Keywords:** {keywords}")
        print("--- Article Start ---")
        # print(article)
        print(f"\n--- Article End ---\nArticle length: {len(article.split())} words")
