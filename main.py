
import time
import asyncio
from utils.logger import logger
from database import database

try:
    from scraper import sky_news_scraper, kooora_scraper
except ImportError as e:
    print(f"Could not import scrapers: {e}")
    sky_news_scraper = None
    kooora_scraper = None

try:
    from generator import content_generator, image_generator
except ImportError as e:
    print(f"Could not import generators: {e}")
    content_generator = None
    image_generator = None

try:
    from publisher import blogger_publisher, telegram_publisher
except ImportError as e:
    print(f"Could not import publishers: {e}")
    blogger_publisher = None
    telegram_publisher = None




def main_cycle():
    """Runs one full cycle of the news bot."""
    logger.info("========================================")
    logger.info("Starting new cycle...")
    
    # 1. Scrape news
    all_articles = []
    try:
        all_articles.extend(sky_news_scraper.scrape_sky_news())
    except Exception as e:
        logger.error(f"Error scraping Sky News: {e}")
        
    try:
        all_articles.extend(kooora_scraper.scrape_kooora())
    except Exception as e:
        logger.error(f"Error scraping Kooora: {e}")

    if not all_articles:
        logger.info("No new articles found in this cycle.")
        return

    # 2. Process each article
    for article_summary in all_articles:
        title = article_summary['title']
        url = article_summary['url']
        source = article_summary['source']

        # 2a. Check if article already exists
        if database.article_exists(url):
            logger.info(f"Article '{title}' already exists. Skipping.")
            continue
        
        logger.info(f"Processing new article: '{title}'")

        # 2b. Generate content
        article_content, keywords = content_generator.generate_article(title, source)
        if not article_content:
            logger.error(f"Failed to generate content for '{title}'. Skipping.")
            continue

        # 2c. Generate image
        image_path = image_generator.generate_post_image(title)
        if not image_path:
            logger.warning(f"Failed to generate image for '{title}'. Proceeding without image.")

        # 2d. Publish to Blogger
        post_url = blogger_publisher.publish_to_blogger(title, article_content, keywords, image_path)
        if not post_url:
            logger.error(f"Failed to publish '{title}' to Blogger. Skipping.")
            continue

        # 2e. Add to database
        database.add_news_article(title, url, source)
        logger.info(f"Successfully added '{title}' to the database.")

        # 2f. Send Telegram notification
        try:
            #         try:
            asyncio.run(telegram_publisher.send_telegram_notification(title, post_url))
        except Exception as e:
            logger.error(f"Failed to send Telegram notification for '{title}': {e}")

        # Add a small delay to avoid overwhelming APIs
        time.sleep(10)
    
    logger.info("Cycle finished.")
    logger.info("========================================\n")

if __name__ == '__main__':
    # Initialize the database
    database.init_db()
    logger.info("Database initialized.")

    # Main loop
    while True:
        try:
            main_cycle()
            # Wait for 1 hour before the next cycle
            logger.info("Sleeping for 1 hour...")
            time.sleep(3600)
        except KeyboardInterrupt:
            logger.info("Bot stopped manually.")
            break
        except Exception as e:
            logger.critical(f"A critical error occurred in the main loop: {e}")
            # Wait for 5 minutes before retrying to avoid spamming errors
            time.sleep(300)
