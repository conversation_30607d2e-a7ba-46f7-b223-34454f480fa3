
from scraper.mock_requests import get, exceptions
from bs4 import BeautifulSoup
from config import SKY_NEWS_URL
from utils.logger import logger

def scrape_sky_news():
    """Scrapes the latest football news from Sky News Arabia.

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting scrape for Sky News Arabia.")
    articles = []
    try:
        response = get(SKY_NEWS_URL, timeout=15)
        response.raise_for_status() # Raise an exception for bad status codes

        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Based on the initial analysis, news items are in 'a' tags with a specific href structure
        news_links = soup.find_all('a', href=True, limit=10)

        for link in news_links:
            title = link.get_text(strip=True)
            url = link.get('href')
            
            # Make sure the URL is absolute
            if url and not url.startswith('http'):
                url = f"https://www.skynewsarabia.com{url}"

            if title and url:
                articles.append({
                    'title': title,
                    'url': url,
                    'source': 'Sky News'
                })
                logger.info(f"Found article: {title}")

    except exceptions.RequestException as e:
        logger.error(f"Failed to scrape Sky News Arabia: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during Sky News scraping: {e}")

    logger.info(f"Found {len(articles)} articles from Sky News Arabia.")
    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_sky_news()
    if scraped_articles:
        for article in scraped_articles:
            print(article)
