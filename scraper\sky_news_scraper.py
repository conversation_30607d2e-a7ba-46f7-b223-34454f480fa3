
from scraper.mock_requests import get, exceptions
from bs4 import BeautifulSoup
from config import SKY_NEWS_URL
from utils.logger import logger

def scrape_sky_news():
    """Scrapes the latest football news from Sky News Arabia.

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting scrape for Sky News Arabia.")
    articles = []
    try:
        response = get(SKY_NEWS_URL, timeout=15)
        response.raise_for_status() # Raise an exception for bad status codes

        soup = BeautifulSoup(response.content, 'html.parser')

        # Try multiple selectors to find news articles
        selectors_to_try = [
            'article h3 a',  # Articles with h3 titles
            'article h2 a',  # Articles with h2 titles
            '.news-item a',  # News items
            '.article-title a',  # Article titles
            'a[href*="/sport/"]',  # Sport section links
            'a[href*="/football"]',  # Football specific links
            'h3 a[href*="/sport/"]',  # Sport headlines
            'h2 a[href*="/sport/"]',  # Sport headlines
            '.headline a',  # Headlines
            '.story-title a'  # Story titles
        ]

        news_links = []
        for selector in selectors_to_try:
            try:
                found_links = soup.select(selector)
                if found_links:
                    logger.info(f"Found {len(found_links)} links with selector: {selector}")
                    news_links.extend(found_links[:10])  # Limit to 10 per selector
                    break  # Use the first working selector
            except Exception as e:
                logger.warning(f"Error with selector {selector}: {e}")
                continue

        # If no specific selectors work, try general approach
        if not news_links:
            logger.warning("No news found with specific selectors, trying general approach")
            all_links = soup.find_all('a', href=True)
            news_links = [link for link in all_links if link.get('href') and
                         ('sport' in link.get('href').lower() or
                          'football' in link.get('href').lower() or
                          'كرة' in link.get_text().lower())][:10]

        for link in news_links:
            try:
                title = link.get_text(strip=True)
                url = link.get('href')

                # Skip navigation and non-news links
                if not title or len(title) < 10:
                    continue

                # Skip common navigation items
                skip_keywords = ['تسجيل الدخول', 'إعدادات', 'راديو', 'مباشر', 'صفحة أخباري', 'البث المباشر']
                if any(keyword in title for keyword in skip_keywords):
                    continue

                # Clean up the title - remove extra text and timestamps
                title = title.split('l')[0]  # Remove timestamp part
                title = title.replace('عاجل', '').strip()  # Remove "urgent" tag
                title = title.replace('كرة قدم أوروبية', '').strip()  # Remove category
                title = title.replace('دوري إسباني', '').strip()
                title = title.replace('دوري إيطالي', '').strip()
                title = title.replace('دوري مصري', '').strip()
                title = title.replace('دوري سعودي', '').strip()
                title = title.replace('رياضة', '').strip()

                # Skip if title becomes too short after cleaning
                if len(title) < 10:
                    continue

                # Make sure the URL is absolute
                if url and not url.startswith('http'):
                    url = f"https://www.skynewsarabia.com{url}"

                # Validate that this looks like a news article URL
                if url and ('sport' in url.lower() or 'football' in url.lower() or 'كرة' in title.lower()):
                    articles.append({
                        'title': title,
                        'url': url,
                        'source': 'Sky News'
                    })
                    logger.info(f"Found article: {title}")
            except Exception as e:
                logger.warning(f"Error processing link: {e}")
                continue

    except exceptions.RequestException as e:
        logger.error(f"Failed to scrape Sky News Arabia: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during Sky News scraping: {e}")

    logger.info(f"Found {len(articles)} articles from Sky News Arabia.")
    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_sky_news()
    if scraped_articles:
        for article in scraped_articles:
            print(article)
