# Web scraping dependencies
requests>=2.31.0
beautifulsoup4>=4.12.0
selenium>=4.15.0
webdriver-manager>=4.0.0

# Google APIs for Blogger
google-auth>=2.23.0
google-auth-oauthlib>=1.1.0
google-auth-httplib2>=0.1.1
google-api-python-client>=2.100.0

# Gemini AI
google-generativeai>=0.3.0

# Telegram bot
python-telegram-bot>=20.6

# Image processing
Pillow>=10.0.0

# Environment variables
python-dotenv>=1.0.0

# Database (SQLite is built-in with Python)
# No additional package needed for SQLite

# Async support
asyncio-mqtt>=0.13.0
