
import google.generativeai as genai
from config import GEMINI_API_KEYS
from utils.logger import logger
import random

class GeminiManager:
    """Manages the pool of Gemini API keys and handles key rotation."""
    def __init__(self):
        if not GEMINI_API_KEYS:
            raise ValueError("No Gemini API keys found in the configuration.")
        self.keys = GEMINI_API_KEYS
        self.current_key_index = random.randint(0, len(self.keys) - 1)

    def get_next_key(self):
        """Rotates to the next key in the list."""
        self.current_key_index = (self.current_key_index + 1) % len(self.keys)
        return self.keys[self.current_key_index]

    def get_model(self):
        """Configures and returns a Gemini model instance with the current API key."""
        max_retries = len(self.keys)
        for _ in range(max_retries):
            try:
                api_key = self.keys[self.current_key_index]
                genai.configure(api_key=api_key)
                model = genai.GenerativeModel('gemini-pro')
                logger.info(f"Successfully configured <PERSON> with key index: {self.current_key_index}")
                return model
            except Exception as e:
                logger.warning(f"Failed to configure Gemini with key index {self.current_key_index}: {e}")
                self.get_next_key() # Rotate to the next key
        
        logger.error("All Gemini API keys failed. Cannot proceed.")
        raise Exception("All Gemini API keys are failing.")

# Instantiate the manager to be used across the application
gemini_manager = GeminiManager()
